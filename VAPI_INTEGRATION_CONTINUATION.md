# VAPI Integration - Continuation Tasks

## 🎯 Current Status
The VAPI integration is working successfully! Voice assistant messages are being synced from Redis and displaying properly in the chat with custom purple styling and title sections.

## ✅ Completed Features
- ✅ Voice assistant messages sync from Redis to chat
- ✅ Custom purple-themed design with "Message from your voice assistant:" title
- ✅ Proper message type separation (voice_assistant vs ai vs system)
- ✅ Redis storage and retrieval working correctly
- ✅ Error handling for JSON parsing issues resolved
- ✅ Metadata extraction (userId, threadId) working properly
- ✅ VAPI function endpoints for real-time data collection
- ✅ Webhook endpoint for call completion processing
- ✅ Sync API for client-side data retrieval

## 🚧 Remaining Tasks

### 1. **Appointment Integration with Care Canvas Focus**
**Issue**: When appointments are synced from Redis, they need to be integrated into the Care Canvas Focus area.

**Current State**: 
- Appointments are being synced and processed in `ChatContext.tsx`
- `handleNewAppointment()` function exists but only logs the data
- Need to integrate with existing Focus/Care Canvas system

**Files to Investigate**:
- `src/contexts/ChatContext.tsx` (line ~320 - handleNewAppointment function)
- Care Canvas Focus component files
- Existing appointment/focus data structures
there's a use-focus-appointments hook that may be useful. have a look at it and also see where and how it's being used elsewhere

### 2. **Transcript Auto-Refresh Issue**
**Issue**: New transcripts show toast notification but don't appear in the "Attach Transcripts" sheet until manual page refresh.

**Current State**:
- Transcripts are being synced and `handleNewTranscript()` is called
- Toast notifications work correctly
- TranscriptSelectionSheet doesn't update automatically

**Files to Investigate**:
- `src/contexts/ChatContext.tsx` (transcript handling)
- `src/components/chat/TranscriptSelectionSheet.tsx`
- Transcript context/state management

### 3. **Smart Sync Timing Optimization**
**Issue**: Currently syncing continuously every 60 seconds + on window focus. More efficient to sync only during active voice sessions.

**Proposed Solution**:
- Start syncing when "Start Session" button is pressed
- Continue syncing for ~8 minutes after session starts
- Stop syncing when timeout reached

**Files to Modify**:
- `src/contexts/ChatContext.tsx` (sync logic around line 348-366)
- `src/components/chat/VoiceAssistantModal.tsx` (session start/end events)



## �️ New API Routes Implemented (what has been implemented)

### 1. **VAPI Send Message Endpoint**
**File**: `src/app/api/vapi/send-message/route.ts`

**Purpose**: Allows VAPI voice assistant to send messages to the chat during active calls.

**How it Works**:
- Receives function calls from VAPI with message content
- Extracts `userId` and `threadId` from VAPI metadata
- Stores message in Redis with `sender: 'voice_assistant'`
- Message gets synced to chat interface automatically

**Key Features**:
- Validates required metadata before storage
- Uses 24-hour expiration (`setex`) for automatic cleanup
- Graceful fallback when Redis unavailable
- Detailed logging for debugging

```typescript
// Example stored data structure:
{
  id: "message_1752517135991_yrhqdc8nr",
  type: "message",
  userId: "user_1752503153836_lx2b4e12c",
  threadId: "xliy62lbv",
  data: {
    content: "hi there up there",
    sender: "voice_assistant",
    messageType: "regular",
    fromCall: true
  }
}
```

### 2. **VAPI Schedule Appointment Endpoint**
**File**: `src/app/api/vapi/schedule-appointment/route.ts`

**Purpose**: Handles appointment scheduling requests from voice assistant during calls.

**How it Works**:
- Processes appointment details (provider, specialty, date, time, type)
- Stores appointment data in Redis for later sync
- Integrates with existing appointment system
- Supports both telehealth and in-person appointments

**Key Features**:
- Validates appointment parameters
- Stores structured appointment data for Care Canvas integration
- Automatic expiration and cleanup
- Consistent metadata handling

### 3. **VAPI Webhook Endpoint**
**File**: `src/app/api/vapi/webhook/route.ts`

**Purpose**: Processes call completion events from VAPI to capture transcripts and call data.

**How it Works**:
- Receives webhook when voice calls end
- Extracts call transcript from VAPI messages
- Processes recording URLs and call metadata
- Stores transcript data for later retrieval

**Key Features**:
- Converts VAPI message format to transcript format
- Handles call analysis and summary data
- Stores recording URLs for playback
- Only processes completed calls (`status: 'ended'`)

```typescript
// Example transcript data structure:
{
  id: "transcript_1752511770301_g7exllsxm",
  type: "transcript",
  userId: "user_1752503153836_lx2b4e12c",
  threadId: "xliy62lbv",
  data: {
    id: "transcript_call_12345",
    summary: "Call completed",
    transcript: "User: Hello\nAI: How can I help you today?",
    startedAt: "2025-07-14T18:15:30.301Z",
    endedAt: "2025-07-14T18:18:55.991Z",
    endedReason: "call-ended",
    recordingUrls: { mono: "...", stereo: "..." }
  }
}
```

### 4. **Sync Pending Items Endpoint**
**File**: `src/app/api/sync/pending/route.ts`

**Purpose**: Client-side API for retrieving and processing pending items from Redis.

**How it Works**:
- **GET**: Retrieves all pending items for a specific user
- **POST**: Marks items as processed and removes them from Redis
- Handles user-scoped data retrieval
- Supports both string and object Redis responses

**Key Features**:
- User-scoped data access via `userId` parameter
- Batch processing of multiple item types
- Automatic cleanup of processed items
- Error handling for malformed data

**Usage Flow**:
1. Client calls GET with `userId` parameter
2. Server retrieves user's pending item list from Redis
3. Server fetches each individual item data
4. Client processes items (adds to chat, focus, etc.)
5. Client calls POST to mark items as delivered
6. Server removes processed items from Redis

## �🔧 Technical Context

### Current Sync Implementation
```typescript
// In ChatContext.tsx
useEffect(() => {
  const interval = setInterval(syncPendingItems, 60000); // Every 60 seconds
  const handleFocus = () => syncPendingItems();
  window.addEventListener('focus', handleFocus);
  syncPendingItems(); // Initial sync
  
  return () => {
    clearInterval(interval);
    window.removeEventListener('focus', handleFocus);
  };
}, [syncPendingItems]);
```

### Message Types Successfully Implemented
```typescript
export interface Message {
  sender: "user" | "ai" | "system" | "voice_assistant";
  content: string | ChatResponsePart[];
  messageType?: "voice_call_start" | "voice_call_end" | "regular";
}
```

### Redis Data Structure
```
user_pending_items:{userId} → Set of pending item IDs
pending_item:{itemId} → JSON data with type: 'message' | 'appointment' | 'transcript'
```

## 🎨 Design Patterns Established

### Voice Assistant Messages
- Purple theme with title section
- Card-style layout with borders
- HandHeart icon for branding
- Separate from regular AI messages

### System Messages
- Blue theme for voice call events
- Compact pill design
- Phone/PhoneOff icons

## 🚀 Next Steps Priority

1. **HIGH**: Fix transcript auto-refresh (impacts user experience)
2. **HIGH**: Implement appointment → Care Canvas integration
3. **MEDIUM**: Optimize sync timing for efficiency

## 📝 Development Notes

- All voice assistant functionality uses `sender: 'voice_assistant'` type
- Redis items expire after 24 hours (using `setex`)
- Sync endpoint accepts `userId` parameter for proper user scoping
- Error handling includes type guards for content arrays vs strings

## 🔍 Key Functions to Continue With

```typescript
// ChatContext.tsx
const handleNewAppointment = (appointmentData: any) => {
  // TODO: Integrate with Care Canvas Focus system
}

const handleNewTranscript = (transcriptData: any) => {
  // TODO: Update transcript state to refresh UI
}

const syncPendingItems = useCallback(async () => {
  // TODO: Make conditional based on voice session state
}
```

---

**Ready to continue development with proper context of completed work and remaining tasks!**
